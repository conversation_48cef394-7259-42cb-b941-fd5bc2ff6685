<template>
	<v-navigation-drawer v-model="close" style="background-color: #ffffff" app light :mini-variant.sync="mini">
		<v-list-item style="padding: 4px 10px;">
			<v-list-item-title class="font text-uppercase font-weight-bold blue-grey--text ml-3">
				{{ profile ? profile.organizationName : 'ADMIN PORTAL' }}
			</v-list-item-title>

			<v-btn icon @click.stop="mini = !mini">
				<v-icon color="#04a9f3">mdi-chevron-left</v-icon>
			</v-btn>
		</v-list-item>

		<v-divider></v-divider>
		<div class="side_bar">
			<v-list dense>
				<v-list-item v-for="item in linkItem" :key="item.title" router class="font" :to="item.link">
					<v-list-item-icon>
						<v-icon>{{ item.icon }}</v-icon>
					</v-list-item-icon>
					<v-list-item-content>
						<v-list-item-title class="font" v-text="item.title"></v-list-item-title>
					</v-list-item-content>
				</v-list-item>

				<v-list-group v-for="item in items" :key="item.title" :prepend-icon="item.icon" no-action>
					<template v-slot:activator>
						<v-list-item-content>
							<v-list-item-title class="font" v-text="item.title"></v-list-item-title>
						</v-list-item-content>
					</template>

					<!-- If subItem has nested items, render a nested group -->
					<v-list-group v-for="subItem in item.items" :key="subItem.title"
						v-if="subItem.items && showDonationGroups && (subItem.title.indexOf('Hour of') !== -1 || subItem.title.indexOf('Coming Of') !== -1)"
						no-action sub-group class="nested-group">
						<template v-slot:activator>
							<v-list-item-content>
								<v-list-item-title class="font blue-grey--text"
									style="text-transform: capitalize; font-size: 12px;" v-text="subItem.title">
								</v-list-item-title>
							</v-list-item-content>
						</template>
						<v-list-item v-for="child in subItem.items" :key="child.title" :to="child.link" router
							class="font">
							<v-list-item-content>
								<v-list-item-title class="font blue-grey--text"
									style="text-transform: capitalize; font-size: 12px;" v-text="child.title">
								</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list-group>

					<!-- Otherwise render a normal link -->
					<v-list-item v-for="subItem in item.items" :key="subItem.title"
						v-if="!(subItem.items && showDonationGroups && (subItem.title.indexOf('Hour of') !== -1 || subItem.title.indexOf('Coming Of') !== -1))"
						class="font" :to="subItem.link" router>
						<v-list-item-content>
							<v-list-item-title class="font blue-grey--text"
								style="text-transform: capitalize; font-size: 12px;" v-text="subItem.title">
							</v-list-item-title>
						</v-list-item-content>
					</v-list-item>
				</v-list-group>
			</v-list>
		</div>
		<v-divider></v-divider>
	</v-navigation-drawer>
</template>

<script>
export default {
	name: 'Drawer',
	props: {
		drawer: {
			type: Boolean,
			default: true,
		},
		profile: {
			type: Object,
			default: null,
		},
	},
	data() {
		return {
			mini: false,
			close: true,
			linkItem: [
				{
					icon: 'mdi-view-dashboard',
					title: 'Dashboard',
					link: { name: 'admin.home.dashboard' },
				},
			],
			items: [
				{
					icon: 'mdi-account-multiple-plus',
					title: 'Staff',
					items: [
						{
							icon: 'mdi-account-group',
							title: 'All Staff',
							link: { name: 'admin.staffs.all' },
						},
					],
				},
				{
					icon: 'mdi-cash-multiple',
					title: 'Transactions',
					items: [
						{
							icon: 'mdi-minus-box',
							title: 'Transactions',
							link: { name: 'admin.transactions.all' },
						},
						{
							icon: 'mdi-minus-box',
							title: 'Exports',
							link: { name: 'admin.transactions.exports' },
						},
						{
							icon: 'mdi-currency-usd',
							title: 'Hour of Solutions Int. Ministries',
							items: [
								{
									icon: 'mdi-cash-multiple',
									title: 'Offering',
									link: { name: 'admin.transactions.hoso.offering' },
								},
								{
									icon: 'mdi-cash-multiple',
									title: 'Tithe',
									link: { name: 'admin.transactions.hoso.tithe' },
								},
							],
						},
						{
							icon: 'mdi-prayer',
							title: 'Coming Of The Lord',
							items: [
								{
									icon: 'mdi-heart',
									title: 'Special Offering',
									link: { name: 'admin.transactions.coming.special' },
								},
								{
									icon: 'mdi-hospital-box',
									title: "Healing Campaign",
									link: { name: 'admin.transactions.coming.campaign' },
								},
							],
						},
					],
				},
				{
					icon: 'delete',
					title: 'Trash',
					items: [
						{
							icon: 'mdi-minus-box',
							title: 'Staff',
							link: { name: 'admin.trash.staff' },
						},
					],
				},
			],
		};
	},
	computed: {
		showDonationGroups() {
			const org = (this.profile && this.profile.organizationName) ? this.profile.organizationName.toLowerCase() : '';
			// match common variants for the organization short name
			return ['hsim', 'hour of solutions', 'hour of solutions int. ministries', 'hour of solutions int ministries'].some(s => org.indexOf(s) !== -1);
		},
	},
	watch: {
		drawer(value) {
			this.close = value;
		},
	},

	created() {
		this.close = this.drawer;
	},
};
</script>

<style scoped>
.nested-group .v-list-item__title {
	font-size: 12px;
}

/* Ensure nested group items are properly indented as children */
.nested-group .v-list-item {
	padding-left: 56px !important;
	/* More indentation for nested children */
}

/* Make sure nested group headers are at the same level as other sub-items */
.nested-group>.v-list-item {
	padding-left: 16px !important;
	/* Same as other sub-items */
}
</style>
