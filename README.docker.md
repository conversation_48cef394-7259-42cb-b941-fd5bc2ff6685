Run the project in Docker (uses Node 16 and Yarn 1)

Prerequisites
- Docker installed and running on your machine.

Quick start

1. Build the image (from project root):

```bash
docker build -t credit-union-app:dev .
```

2. Run with docker-compose (recommended):

```bash
docker-compose up --build
```

3. Visit the dev server at http://localhost:8080

Notes
- The project folder is mounted into the container; changes on the host will be reflected in the container.
- If you prefer not to mount node_modules from host, delete the `- /usr/src/app/node_modules` line in `docker-compose.yml`.
- If Docker isn't available, you can use a local Node version manager (nvm) to run Node 16.
