{"name": "admin-portal", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"apexcharts": "^3.28.1", "axios": "^0.21.1", "axios-cancel": "^0.2.2", "core-js": "^3.8.3", "lodash": "^4.17.21", "material-design-icons-iconfont": "^6.1.0", "moment": "^2.29.1", "node-sass": "^6.0.1", "nprogress": "^0.2.0", "rxjs": "^7.3.0", "secure-ls": "^1.2.6", "vee-validate": "^3.4.12", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-router": "^3.5.2", "vue-router-sync": "^0.1.0", "vuetify": "^2.4.0", "vuex": "^3.6.2", "vuex-persistedstate": "^4.0.0", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "prettier": "^1.19.1", "sass": "^1.19.0", "sass-loader": "^8.0.0", "vue-cli-plugin-vuetify": "~2.0.7", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.3.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}