name: Donations Deployment

on:
  push:
    branches:
      - app/donations
    

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14.16.1]

    steps:
      - name: Install SSH Keys
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
          known_hosts: "placeholder-for-known-hosts"

      - name: Adding Known Hosts
        run: ssh-keyscan -H ${{ secrets.PROD_DEPLOY_HOST }} >> ~/.ssh/known_hosts
      - name: Checking out Food Services Web Codes
        uses: actions/checkout@v2

      - name: Using Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: yarn
      - run: yarn install

      - name: Building The UI Codes For Production
        env:
          VUE_APP_BASE_URL: ${{ secrets.VUE_APP_BASE_URL }}
        run: yarn run build

      - name: Deploy To Kairos Production Server
        run: rsync -avz  dist/* ${{ secrets.DEPLOY_USER }}@${{ secrets.PROD_DEPLOY_HOST }}:/home/<USER>/clients/donations