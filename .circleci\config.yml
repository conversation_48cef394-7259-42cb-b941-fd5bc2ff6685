# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2
jobs:
  build:
    docker:
      # specify the version you desire here
      - image: circleci/node:10

    working_directory: ~/frontend-service

    steps:
      - checkout
      - attach_workspace:
          at: ~/frontend-service

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v1-dependencies-

      #      - run:
      #          name: Installing vue globally and caching dependencies
      #          command: |
      #            npm set prefix=/home/<USER>/npm && echo 'export PATH=$HOME/circleci/npm/bin:$PATH' >> /home/<USER>/.bashrc
      #            npm install -g @vue/cli

      - run:
          name: Installing the package.json dependencies
          command: npm install

      - save_cache:
          paths:
            - ./node_modules
          key: v1-dependencies-{{ checksum "package.json" }}
      # run build!
      - run: 
          name: Bundling the app for production
          command: npm run build
          environment:
            VUE_APP_BASE_URL: https://ussd.kairosafrika.com
      - persist_to_workspace:
          root: .
          paths: dist
      # run tests!
      #- run: yarn test

  deploy:
    machine:
      enabled: true
    working_directory: ~/frontend-service
    steps:
      - checkout
      - attach_workspace:
          at: ~/frontend-service
      - add_ssh_keys:
          fingerprints:
            - "93:41:b4:4c:b7:c8:10:c1:c0:52:84:e6:16:5c:73:2e"
      - run:
          name: Deploying To Kairos Production
          command: |
            rsync -a dist/* $DEPLOY_USER@$PROD_DEPLOY_HOST:/home/<USER>/clients/tickets

workflows:
  version: 2
  build-and-deploy:
    jobs:
      - build
      - deploy:
          requires:
            - build
          filters:
            branches:
              only: app/tickets
