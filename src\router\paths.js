export default [
	{
		path: '/',
		name: 'root',
		redirect: '/account/login',
	},
	{
		path: '/account',
		name: 'account',
		component: () => import(`@/components/greeters/LandingTemplate`),
		children: [
			{
				path: '/account/login',
				name: 'account.login',
				component: () => import(`@/components/greeters/Login`),
				meta: {
					requiresAuth: false,
				},
			},
		],
	},
	//back office routes for Admin after successful login
	{
		path: '/admin',
		name: 'admin',
		component: () => import(`@/components/views/layout/AppTemplate`),
		children: [
			{
				path: '/admin/home/<USER>',
				name: 'admin.home.dashboard',
				component: () => import(`@/components/roles/admin/home/<USER>
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/staffs/all',
				name: 'admin.staffs.all',
				component: () => import(`@/components/roles/admin/staffs/AllStaff`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/staffs/add',
				name: 'admin.staffs.add',
				component: () =>
					import(`@/components/roles/admin/staffs/AddAndEditStaffDialog`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/staffs/:id/details',
				name: 'admin.staffs.details',
				component: () => import(`@/components/roles/admin/staffs/Details`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/staffs/deleted-staff',
				name: 'admin.staffs.deleted-staff',
				component: () => import('@/components/roles/admin/staffs/RemovedStaff'),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/customers/all',
				name: 'admin.customers.all',
				component: () =>
					import(`@/components/roles/admin/customers/ActiveCustomers`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/customers/new',
				name: 'admin.customers.new',
				component: () =>
					import(`@/components/roles/admin/customers/AddAndEditMemberPage`),
				meta: {
					requiresAuth: true,
				},
			},

			{
				path: '/admin/tickets/all',
				name: 'admin.balances.all',
				component: () =>
					import(`@/components/roles/admin/transactions/Tickets`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/verify/tickets',
				name: 'admin.verify.tickets',
				component: () =>
					import(`@/components/roles/admin/transactions/VerifyTicket`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/transactions/all',
				name: 'admin.transactions.all',
				component: () =>
					import(`@/components/roles/admin/transactions/Transactions`),
				meta: {
					requiresAuth: true,
				},
			},
			// Donation-specific transaction views
			{
				path: '/admin/transactions/hoso/offering',
				name: 'admin.transactions.hoso.offering',
				component: () =>
					import(`@/components/roles/admin/transactions/Transactions`),
				meta: {
					requiresAuth: true,
					donationPortal: 'hoso',
					donationType: 'offering',
					account: {
						bank: 'Universal Merchant Bank (UMB) - Kaneshie Branch',
						name: 'HOUR OF SOLUTIONS INT. MINISTRIES',
						number: '*************',
					},
				},
			},
			{
				path: '/admin/transactions/hoso/tithe',
				name: 'admin.transactions.hoso.tithe',
				component: () =>
					import(`@/components/roles/admin/transactions/Transactions`),
				meta: {
					requiresAuth: true,
					donationPortal: 'hoso',
					donationType: 'tithe',
					account: {
						bank: 'Universal Merchant Bank (UMB) - Kaneshie Branch',
						name: 'HOUR OF SOLUTIONS INT. MINISTRIES',
						number: '*************',
					},
				},
			},
			{
				path: '/admin/transactions/coming/special',
				name: 'admin.transactions.coming.special',
				component: () =>
					import(`@/components/roles/admin/transactions/Transactions`),
				meta: {
					requiresAuth: true,
					donationPortal: 'coming',
					donationType: 'special',
					account: {
						bank: 'Universal Merchant Bank (UMB) - Kaneshie Branch',
						name: 'COMING OF THE LORD',
						number: '*************',
					},
				},
			},
			{
				path: '/admin/transactions/coming/campaign',
				name: 'admin.transactions.coming.campaign',
				component: () =>
					import(`@/components/roles/admin/transactions/Transactions`),
				meta: {
					requiresAuth: true,
					donationPortal: 'coming',
					donationType: 'campaign',
					account: {
						bank: 'Universal Merchant Bank (UMB) - Kaneshie Branch',
						name: 'COMING OF THE LORD',
						number: '*************',
					},
				},
			},

			{
				path: '/admin/transactions/exports',
				name: 'admin.transactions.exports',
				component: () =>
					import(`@/components/roles/admin/transactions/Exports`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/trash/staff',
				name: 'admin.trash.staff',
				component: () => import(`@/components/roles/admin/trash/Staff`),
				meta: {
					requiresAuth: true,
				},
			},
			{
				path: '/admin/trash/members',
				name: 'admin.trash.members',
				component: () => import(`@/components/roles/admin/trash/Members`),
				meta: {
					requiresAuth: true,
				},
			},
		],
	},
];
