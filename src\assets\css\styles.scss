$font-family: '<PERSON><PERSON><PERSON>';
body {
  font-family: $font-family, sans-serif;
  font-size: 1.2rem;
}

.font {
  font-size: 1.4rem;
  font-family: $font-family, sans-serif;
  &-sm {
    font-size: 0.8rem;
  }
  &-md {
    font-size: 1rem;
  }
}

.box-shadow {
  -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.07) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.07) !important;
  &-light {
    -webkit-box-shadow: 0 1px 3px 1px rgba(60, 64, 67, 0.15) !important;
    box-shadow: 0 1px 3px 1px rgba(60, 64, 67, 0.15) !important;
  }
}

.custom-shadow {
  -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.v--shadow-dialog {
  -webkit-box-shadow: rgba(9, 30, 66, 0.25) 0 4px 8px -2px,
    rgba(9, 30, 66, 0.31) 0px 0px 1px !important;
  box-shadow: rgba(9, 30, 66, 0.25) 0 4px 8px -2px,
    rgba(9, 30, 66, 0.31) 0px 0px 1px !important;
}

.v--shadow-light {
  -webkit-box-shadow: rgba(9, 30, 66, 0.31) 0 0 1px 0,
    rgba(9, 30, 66, 0.25) 0 1px 1px 0;
  box-shadow: rgba(9, 30, 66, 0.31) 0 0 1px 0, rgba(9, 30, 66, 0.25) 0 1px 1px 0;
}

.v-dialog {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  -moz-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  -o-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  border-radius: 10px !important;
  background-color: #fff;
}

.v-menu__content {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: rgba(9, 30, 66, 0.25) 0 4px 8px -2px,
    rgba(9, 30, 66, 0.31) 0px 0px 1px !important;
}

.v-tooltip__content {
  padding: 1px 8px !important;
  background: #2d2d2d !important;
}
