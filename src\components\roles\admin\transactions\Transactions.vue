<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <div class="d-flex flex-row flex-grow-1 justify-space-between">
          <h3
            class="font-weight-bold blue-grey--text mt-2 font font-md font-weight-medium text-uppercase"
          >
            {{ pageTitle }}
          </h3>
          <v-menu
            ref="menu"
            v-model="menu"
            :close-on-content-click="false"
            :return-value.sync="dates"
            transition="scale-transition"
            offset-y
            class="font font-weight-medium"
            min-width="auto"
            :nudge-left="120"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-chip
                v-model="dates"
                v-bind="attrs"
                v-on="on"
                label
                :close="dates.length === 2"
                @click:close="(dates = []), (chosenDateRange = [])"
                :color="dates.length > 0 ? 'primary' : 'white'"
                class=" box-shadow-light mt-2 font text-uppercase font-weight-medium"
              >
                <i class="material-icons-outlined font-size-md mr-2">event</i>
                {{ dates.length > 0 ? dateRangeText : "Choose date range" }}
              </v-chip>
            </template>
            <v-date-picker
              class="font font-weight-medium font-size-sm"
              v-model="dates"
              range
            >
              <v-spacer></v-spacer>
              <v-btn
                small
                class="font font-weight-medium"
                text
                color="primary"
                @click="(menu = false), (date = []), (chosenDateRange = [])"
              >
                Cancel
              </v-btn>
              <v-btn
                @click="setFilteredDate"
                text
                class="font font-weight-medium"
                color="primary"
              >
                OK
              </v-btn>
            </v-date-picker>
          </v-menu>
        </div>
      </v-col>

      <!-- Account info display for donation portals - only show for HOSIM organization -->
      <v-col cols="12" class="mb-3" v-if="shouldShowAccountDetails">
        <v-card class="pa-3 box-shadow-light">
          <div>
            <h4 class="font-weight-medium">Account Details</h4>
            <p><strong>Bank:</strong> {{ $route.meta.account.bank }}</p>
            <p><strong>Account Name:</strong> {{ $route.meta.account.name }}</p>
            <p>
              <strong>Account Number:</strong> {{ $route.meta.account.number }}
            </p>
          </div>
        </v-card>
      </v-col>

      <v-col cols="12" sm="12">
        <div
          class="d-flex flex-row flex-grow-1 justify-space-between align-center"
        >
          <!-- Payment Status Filter -->
          <v-select
            v-model="paymentStatusFilter"
            :items="paymentStatusOptions"
            label="Filter by Payment Status"
            solo
            flat
            hide-details
            class="box-shadow-light font font-sm mr-3"
            style="max-width: 250px"
          ></v-select>

          <!-- Search and Refresh -->
          <div class="d-flex flex-row">
            <v-text-field
              solo
              rounded
              placeholder="Search through transactions using account number ..."
              flat
              append-icon="search"
              v-model="search"
              hide-details
              class="box-shadow-light font font-sm"
              style="min-width: 400px"
            >
            </v-text-field>
            <v-tooltip right>
              <template v-slot:activator="{ on }">
                <v-btn
                  :loading="pageLoading"
                  class="mx-2 mt-1"
                  @click="refreshTransactions"
                  v-on="on"
                  small
                  fab
                  icon
                >
                  <i class="material-icons-outlined">refresh</i>
                </v-btn>
              </template>
              <span class=" font font-sm">Get latest transactions</span>
            </v-tooltip>
          </div>
        </div>
      </v-col>

      <!-- Total Amount Display -->
      <v-col cols="12" v-if="filteredTransactions.length > 0">
        <v-card class="pa-3 box-shadow-light">
          <div class="d-flex justify-space-between align-center">
            <h4 class="font-weight-medium">Transaction Summary</h4>
            <div class="text-right">
              <h3 class="font-weight-bold primary--text">
                Total: GHC {{ totalAmount.toLocaleString() }}
              </h3>
              <small class="grey--text"
                >{{ filteredTransactions.length }} transaction(s)</small
              >
            </div>
          </div>
        </v-card>
      </v-col>

      <v-col cols="12" sm="12" lg="12" md="12" xl="12">
        <v-data-table
          :headers="headers"
          :items="filteredTransactions"
          :loading="pageLoading"
          item-key="branch"
          :server-items-length="paginate.total"
          :options.sync="options"
          class="font text-capitalize box-shadow-light mx-1 mt-n2"
          loading-text="Loading... Please wait"
          :footer-props="{
            itemsPerPageOptions: [30, 40, 50]
          }"
        >
          <template #item.totalAmount="{ item }">
            <span class=" font-sm font-weight-medium"
              >GHC {{ item.totalAmount ? item.totalAmount : "N/A" }}
            </span>
          </template>
          <template #item.activity="{ item }">
            <v-chip label small color="primary">
              <span class=" font text-uppercase font-sm font-weight-medium">
                {{ item.activity }}
              </span>
            </v-chip>
          </template>
          <template #item.paymentStatus="{ item }">
            <v-chip
              v-if="
                item.paymentStatus &&
                  item.paymentStatus.toLowerCase() === 'success'
              "
              label
              small
              :color="'#06c90e'"
            >
              <span
                class=" font font-weight-medium font-sm text-uppercase white--text"
                >{{ "Successful" }}</span
              >
            </v-chip>
            <v-chip
              v-else-if="
                item.paymentStatus &&
                  item.paymentStatus.toLowerCase() === 'failed'
              "
              label
              small
              :color="'error'"
            >
              <span class=" font font-weight-medium font-sm text-uppercase">{{
                "Failed"
              }}</span>
            </v-chip>
            <v-chip v-else color="primary" label small>
              <span class="font font-weight-medium font-sm text-uppercase">
                N/A
              </span>
            </v-chip>
          </template>
          <template #item.createdAt="{ item }">
            <span class=" font font-weight-medium font-sm">
              {{ item.createdAt | dateFormat }}
            </span>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
export default {
  name: "Transactions",

  data() {
    return {
      search: "",
      options: {
        page: 1,
        itemsPerPage: 30
      },
      menu: false,
      dates: [],
      chosenDateRange: [],
      paymentStatusFilter: "all",
      paymentStatusOptions: [
        { text: "All", value: "all" },
        { text: "Successful", value: "success" },
        { text: "Failed", value: "failed" }
      ]
    };
  },

  computed: {
    ...mapGetters({
      pageLoading: "getIsPageLoading",
      paginate: "transactions/getPagination",
      transactions: "transactions/getAllTransactions",
      userDetails: "auth/getUserDetails"
    }),
    pageTitle() {
      const meta = this.$route.meta || {};
      if (meta.donationType) {
        const labels = {
          offering: "Offering Transactions",
          tithe: "Tithe Transactions",
          special: "Special Offering Transactions",
          campaign: "Healing Campaign Transactions"
        };
        return labels[meta.donationType] || "Transactions";
      }
      return "All Transactions";
    },
    // Filter transactions based on payment status and activity
    filteredTransactions() {
      let filtered = this.transactions;

      // Apply activity filter (client-side fallback in case server doesn't filter)
      if (this.activityFilter) {
        filtered = filtered.filter(transaction => {
          if (!transaction.activity) return false;
          return transaction.activity === this.activityFilter;
        });
      }

      // Apply payment status filter (client-side)
      if (this.paymentStatusFilter !== "all") {
        filtered = filtered.filter(transaction => {
          if (!transaction.paymentStatus) return false;
          const status = transaction.paymentStatus.toLowerCase();
          return status === this.paymentStatusFilter;
        });
      }

      return filtered;
    },

    // Calculate total amount from filtered transactions
    totalAmount() {
      return this.filteredTransactions.reduce((sum, transaction) => {
        const amount = parseFloat(transaction.totalAmount) || 0;
        return sum + amount;
      }, 0);
    },

    // Check if account details should be shown (only for HOSIM organization with route meta account)
    shouldShowAccountDetails() {
      // First check if route has account meta
      if (!this.$route.meta || !this.$route.meta.account) {
        return false;
      }

      // Only show for HOSIM organization (using organizationName from user details)
      // This ensures reusability - other organizations will see the default view
      const userOrgName = this.userDetails?.organizationName || "";
      const isHosimOrg =
        userOrgName.toUpperCase().includes("HOSIM") ||
        userOrgName.toUpperCase().includes("HOUR OF SOLUTIONS");

      return isHosimOrg;
    },

    // Get the activity filter value based on current route
    activityFilter() {
      const meta = this.$route.meta || {};
      if (!meta.donationType) return null;
      const mapTypeToActivity = {
        offering: "Offering",
        tithe: "Tithe",
        special: "Special Offering",
        campaign: "Coming of the Lord's Healing Campaign"
      };
      return mapTypeToActivity[meta.donationType] || null;
    },
    dateRangeText() {
      return this.dates.join(" ~ ");
    },
    transactionsList() {
      return this.transactions.filter(searchTerm => {
        return (
          searchTerm.sequenceID
            .toLowerCase()
            .indexOf(this.search.toLowerCase()) !== -1
        );
      });
    },

    headers() {
      return [
        {
          text: "Date",
          value: "createdAt",
          filterable: false,
          sortable: false
        },
        {
          text: "Account Number",
          value: "accountNumber",
          filterable: true,
          sortable: false
        },

        {
          text: "Total Amount (GHS)",
          value: "totalAmount",
          filterable: true,
          sortable: false
        },
        {
          text: "Activity",
          value: "activity",
          sortable: false
        },
        {
          text: "Payment Status",
          value: "paymentStatus",
          sortable: false
        }
      ];
    }
  },
  filters: {
    status(value) {
      return !value ? "Active" : "Inactive";
    },
    firstLetterFunction(value) {
      return value.charAt(0).toUpperCase();
    },
    dateFormat(value) {
      return moment(value).format("Do MMM,YYYY HH:MM:ss A");
    }
  },

  watch: {
    search(searchTerm) {
      if (searchTerm !== "") {
        const query = this.buildQuery({ searchTerm, page: 1, size: 30 });
        this.$store.dispatch("transactions/searchList", query);
      } else {
        const query = this.buildQuery({ page: 1, size: 30 });
        this.$store.dispatch("transactions/list", query);
      }
    },
    options: {
      handler(value) {
        const { page, itemsPerPage } = value;
        const query = this.buildQuery({ page, size: itemsPerPage });
        this.$store.dispatch("transactions/list", query);
      },
      deep: true
    },
    chosenDateRange(dates) {
      if (dates.length > 1) {
        const [startDate, endDate] = dates;
        const query = this.buildQuery({
          page: 1,
          size: 30,
          startDate,
          endDate
        });
        this.$store.dispatch("transactions/list", query);
      } else {
        const query = this.buildQuery({ page: 1, size: 30 });
        this.$store.dispatch("transactions/list", query);
      }
    },
    // Watch for route changes to refresh data with new activity filter
    "$route.meta.donationType"() {
      const query = this.buildQuery({ page: 1, size: 30 });
      this.$store.dispatch("transactions/list", query);
    }
  },

  methods: {
    // Build query string with all parameters including activity filter
    buildQuery(params = {}) {
      const queryParams = new URLSearchParams();

      // Add activity filter if route specifies donation type
      if (this.activityFilter) {
        queryParams.append("activity", this.activityFilter);
      }

      // Add other parameters
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value);
        }
      });

      return `?${queryParams.toString()}`;
    },

    setFilteredDate() {
      this.$refs.menu.save(this.dates);
      this.chosenDateRange = this.dates;
    },

    refreshTransactions() {
      const query = this.buildQuery({ page: 1, size: 30 });
      this.$store.dispatch("transactions/list", query);
    }
  },
  created() {
    const query = this.buildQuery({ page: 1, size: 30 });
    this.$store.dispatch("transactions/list", query);
  }
};
</script>

<style scoped>
.createNew {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: stretch;
  align-content: stretch;
}

.box {
  height: auto;
  width: auto;
  padding: 5%;
}
</style>
