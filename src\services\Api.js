import axios from 'axios';
import router from '@/router/index';
import SecureLS from 'secure-ls';
const ls = new SecureLS({
  isCompression: false,
});
let HttpRequest = () => {
  // Resolve API base URL with the following precedence:
  // 1. VUE_API_BASE_URL (recommended for production .env files)
  // 2. VUE_APP_BASE_URL (legacy variable used previously)
  // 3. local fallback (developer machine)
  // To force a specific URL locally, you can uncomment the FORCE_API_BASE_URL line below.
  // const FORCE_API_BASE_URL = 'https://kamccu.kairosafrika.com/v1/api';
  let baseUrl = process.env.VUE_API_BASE_URL || process.env.VUE_APP_BASE_URL || 'http://localhost:8000/v1/api';
  // If you'd like to force the production url regardless of env vars, uncomment:
  // baseUrl = process.env.FORCE_API_BASE_URL || FORCE_API_BASE_URL || baseUrl;

  let Instance = axios.create({
    baseURL: baseUrl,
    timeout: 60000,
  });

  Instance.interceptors.request.use(
    function(config) {
      config.headers.common['Accept'] = 'application/json';
      config.headers.common['Content-Type'] = 'application/json';
      const session = JSON.parse(ls.get('cloud.access'))?.auth?.user?.token;
      if (session !== null || session !== undefined) {
        config.headers.common['authorization'] = `Bearer ${session}`;
      }
      return config;
    },
    function(error) {
      return Promise.reject(error);
    },
  );

  Instance.interceptors.response.use(
    function(response) {
      return response;
    },
    function(error) {
      if (error.response) {
        if (error.response.status === 401) {
          localStorage.clear();
          router.replace({ name: 'account.login' });
        }
      }
      return Promise.reject(error);
    },
  );

  return Instance;
};

export default HttpRequest;
