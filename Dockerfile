FROM node:16-bullseye

# Use a consistent working directory inside the container
WORKDIR /usr/src/app

# Enable corepack and activate Yarn 1 (project uses yarn v1 in logs)
RUN corepack enable && corepack prepare yarn@1.22.22 --activate

# Copy package manifests first to install dependencies (leverages layer cache)
COPY package.json yarn.lock* ./

# Install deps. Some transitive packages may declare newer engine requirements
# than the project's Node version; pass --ignore-engines so install completes
# (we run in Node 16 to match project expectations).
RUN yarn install --ignore-engines --frozen-lockfile || yarn install --ignore-engines

# Copy the rest of the project
COPY . .

# Expose the dev server port
EXPOSE 8080

# Make the dev server reachable from host
ENV HOST=0.0.0.0

# Default command for development
CMD ["yarn", "serve"]
